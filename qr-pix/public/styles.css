/* Reset e Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    max-width: 1400px;
    margin: 0 auto;
    min-height: 100vh;
    gap: 2rem;
    padding: 2rem;
}

/* Form Container */
.form-container {
    background: rgba(17, 17, 17, 0.95);
    border-radius: 20px;
    padding: 2.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    height: fit-content;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h1 {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
}

/* Form Styles */
.pix-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

label {
    color: white;
    font-weight: 500;
    font-size: 0.9rem;
}

input, select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 0.875rem 1rem;
    color: white;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

input:focus, select:focus {
    outline: none;
    border-color: #667eea;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

select option {
    background: #2d3748;
    color: white;
}

small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    margin-top: -0.25rem;
}

.char-counter {
    text-align: right;
}

.validation-message {
    font-size: 0.8rem;
    margin-top: 0.25rem;
    min-height: 1.2rem;
}

.validation-message.error {
    color: #ef4444;
}

.validation-message.success {
    color: #10b981;
}

/* Style Section */
.style-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.style-section h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Generate Button */
.generate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    color: white;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
    margin-top: 1rem;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.generate-btn:active {
    transform: translateY(0);
}

.generate-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.generate-btn.loading .btn-text {
    opacity: 0;
}

.generate-btn.loading .loading-spinner {
    display: block;
}

/* Result Container */
.result-container {
    background: rgba(17, 17, 17, 0.95);
    border-radius: 20px;
    padding: 2.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

/* QR Placeholder */
.qr-placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.qr-placeholder h3 {
    color: white;
    margin: 1rem 0 0.5rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.qr-placeholder p {
    font-size: 1rem;
    line-height: 1.5;
}

.qr-icon {
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* QR Result */
.qr-result {
    width: 100%;
    max-width: 400px;
}

.qr-image-container {
    position: relative;
    background: white;
    border-radius: 16px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.qr-image-container img {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

.download-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.download-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-1px);
}

.qr-info h3 {
    color: #10b981;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.br-code-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    text-align: left;
}

.br-code-container label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    font-weight: 500;
    display: block;
    margin-bottom: 0.5rem;
}

.br-code-text {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 0.75rem;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: white;
    word-break: break-all;
    margin-bottom: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.copy-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.copy-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.pix-details {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1rem;
    text-align: left;
}

.pix-detail-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.pix-detail-item:last-child {
    border-bottom: none;
}

.pix-detail-label {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.pix-detail-value {
    color: white;
    font-weight: 600;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: rgba(17, 17, 17, 0.95);
    border-radius: 16px;
    max-width: 500px;
    margin: 5% auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    color: #ef4444;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.modal-body {
    padding: 1rem 2rem 2rem;
}

.modal-body p {
    color: white;
    line-height: 1.6;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: #10b981;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
    z-index: 1001;
    opacity: 0;
    transform: translateX(100px);
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast.error {
    background: #ef4444;
    box-shadow: 0 10px 30px rgba(239, 68, 68, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-container,
    .result-container {
        padding: 2rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .form-container,
    .result-container {
        padding: 1.5rem;
        border-radius: 16px;
    }
    
    .form-header h1 {
        font-size: 1.5rem;
    }
    
    .modal-content {
        margin: 10% 1rem;
    }
}

@media (max-width: 480px) {
    .form-container,
    .result-container {
        padding: 1rem;
    }
    
    .toast {
        right: 1rem;
        left: 1rem;
        top: 1rem;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles */
button:focus-visible,
input:focus-visible,
select:focus-visible {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .form-container,
    .result-container {
        border: 2px solid white;
    }
    
    input,
    select {
        border: 2px solid rgba(255, 255, 255, 0.5);
    }
}